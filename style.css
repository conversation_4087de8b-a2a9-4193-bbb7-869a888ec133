* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    height: 100vh;
    overflow: hidden;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

/* 背景图片 */
.background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:url('./webgl/bg.png') no-repeat center bottom;;
    background-size: cover;
    background-position: center;
    z-index: 1;
}


/* 内容容器 */
.content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 50px 20px;
}

/* 头部区域 */
.header {
    margin-bottom: 100px;
}

/* Logo样式 */
.logo {
    margin-bottom: 30px;
}

.logo-img {
    width: 200px;
    height: 200px;
  
}

/* 开始实验按钮 */
.start-button {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border: none;
    padding: 20px 60px;
    font-size: 24px;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    letter-spacing: 2px;
    min-width: 200px;
}

.start-button:hover {
    background: linear-gradient(135deg, #357abd, #2c5aa0);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.start-button:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 进度条样式 */
.progress-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 100px;
}

.progress-wrapper {
    text-align: center;
    min-width: 400px;
}

.progress-bar-image {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.progress-bg {
    display: block;
    width: 400px;
    height: auto;
}

.progress-fill-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.progress-fill-img {
    width: 400px;
    height: auto;
    clip-path: inset(0 100% 0 0);
    transition: clip-path 0.3s ease;
}

.progress-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 400px;
    height: auto;
}

.progress-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title {
        font-size: 32px;
        letter-spacing: 2px;
    }
    
    .logo-img {
        width: 80px;
        height: 80px;
    }
    
    .start-button {
        padding: 15px 40px;
        font-size: 20px;
        min-width: 160px;
    }
    
    .header {
        margin-bottom: 60px;
    }
    
    .button-container {
        margin-top: 60px;
    }

    .progress-wrapper {
        min-width: 300px;
    }

    .progress-bg,
    .progress-fill-img,
    .progress-top {
        width: 300px;
    }

    .progress-text {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 24px;
        letter-spacing: 1px;
    }
    
    .start-button {
        padding: 12px 30px;
        font-size: 18px;
        min-width: 140px;
    }

    .progress-wrapper {
        min-width: 250px;
    }

    .progress-bg,
    .progress-fill-img,
    .progress-top {
        width: 250px;
    }

    .progress-text {
        font-size: 14px;
    }
}
