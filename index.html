<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离心式风机启动实验</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 背景图片容器 -->
        <div class="background-image"></div>
        
        <!-- 内容容器 -->
        <div class="content">
            <!-- 顶部logo和标题 -->
            <div class="header">
                <div class="logo">
                    <img src="./webgl/logo.png" alt="核电实验Logo" class="logo-img">
                </div>
                <h1 class="title">离心式风机启动实验</h1>
            </div>
            
            <!-- 开始实验按钮 -->
            <div class="button-container" id="buttonContainer">
                <button class="start-button" onclick="startExperiment()">开始实验</button>
            </div>

            <!-- 进度条容器 -->
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-wrapper">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">正在加载实验环境... 0%</div>
                </div>
            </div>
        </div>
        
    </div>

    <script>
        function startExperiment() {
            // 隐藏按钮，显示进度条
            document.getElementById('buttonContainer').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'block';

            // 开始进度条动画
            startProgressAnimation();
        }

        function startProgressAnimation() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            let progress = 0;

            const interval = setInterval(() => {
                progress += Math.random() * 3 + 1; // 随机增加1-4%

                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);

                    // 进度完成后的处理
                    setTimeout(() => {
                        progressText.textContent = '加载完成！正在进入实验...';
                        setTimeout(() => {
                            // 这里可以添加跳转到实验页面的逻辑
                            // window.location.href = 'experiment.html';
                            alert('实验环境加载完成！');
                        }, 1000);
                    }, 500);
                }

                // 更新进度条
                progressFill.style.width = progress + '%';
                progressText.textContent = `正在加载实验环境... ${Math.floor(progress)}%`;

            }, 100); // 每100ms更新一次
        }
    </script>
</body>
</html>
